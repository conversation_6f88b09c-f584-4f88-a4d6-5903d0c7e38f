{"name": "a8f71e05-efcf4d4a", "frames": [{"timestamp": 10000, "objects": [{"category": "car", "id": 0, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 557.746057, "y1": 321.920543, "x2": 627.620283, "y2": 366.83969}}, {"category": "truck", "id": 1, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 494.110601, "y1": 314.434019, "x2": 576.462368, "y2": 364.344181}}, {"category": "car", "id": 2, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 431.722899, "y1": 321.920543, "x2": 516.570174, "y2": 356.857657}}, {"category": "car", "id": 3, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 406.767818, "y1": 321.920543, "x2": 454.182472, "y2": 351.86664}}, {"category": "car", "id": 4, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 4.991016, "y1": 321.920543, "x2": 217.109204, "y2": 405.520064}}, {"category": "area/drivable", "id": 5, "attributes": {}, "poly2d": [[400.529048, 595.178678, "L"], [401.776802, 516.570174, "C"], [385.555999, 436.713915, "C"], [370.582951, 364.34418, "C"], [393.042523, 356.857656, "L"], [1280.195648, 566.480335, "L"], [1280.195648, 595.178678, "L"], [1169.145538, 590.187662, "C"], [1054.352166, 586.4444, "C"], [923.337992, 591.435416, "C"], [724.945099, 576.462368, "C"], [600.169695, 566.480335, "C"], [400.529048, 595.178678, "C"]]}, {"category": "lane/double yellow", "id": 6, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[340.636854, 600.169695, "C"], [350.618886, 441.704931, "C"], [380.564983, 361.848672, "C"], [339.3891, 359.353164, "L"]]}, {"category": "lane/double yellow", "id": 7, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[405.520064, 590.187662, "C"], [368.087443, 383.060491, "C"], [386.803753, 356.857657, "C"], [341.884608, 351.86664, "L"]]}]}], "attributes": {"weather": "clear", "scene": "residential", "timeofday": "daytime"}}