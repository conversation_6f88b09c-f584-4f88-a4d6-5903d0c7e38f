#!/usr/bin/env python3
"""
测试单图片迭代可视化修复效果
验证TensorBoard每个迭代步骤只显示一张图片
"""
import torch
import torch.nn as nn
import numpy as np
import time
from pathlib import Path
from torch.utils.tensorboard import SummaryWriter
import matplotlib.pyplot as plt
import cv2

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SingleImageVisualizationTest:
    """单图片可视化测试类"""
    
    def __init__(self):
        # 创建测试目录
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        self.test_dir = Path(f"unified_runs/single_image_test_{timestamp}")
        self.test_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化TensorBoard
        self.writer = SummaryWriter(self.test_dir / "tensorboard")
        
        print(f"🧪 单图片可视化测试")
        print(f"📁 测试目录: {self.test_dir}")
        print(f"📈 TensorBoard: tensorboard --logdir {self.test_dir / 'tensorboard'}")
    
    def create_test_images(self, batch_size: int = 4) -> torch.Tensor:
        """创建测试图像"""
        # 创建不同颜色的测试图像
        images = []
        colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # 红绿蓝黄
        
        for i in range(batch_size):
            # 创建纯色背景
            img = np.zeros((224, 224, 3), dtype=np.uint8)
            color = colors[i % len(colors)]
            img[:, :] = color
            
            # 添加文字标识
            cv2.putText(img, f"Image {i+1}", (50, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
            cv2.putText(img, f"Batch Index: {i}", (30, 150), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # 转换为tensor格式
            img_tensor = torch.from_numpy(img).permute(2, 0, 1).float() / 255.0
            images.append(img_tensor)
        
        return torch.stack(images)
    
    def create_test_targets(self, batch_size: int = 4) -> list:
        """创建测试目标"""
        targets = []
        for i in range(batch_size):
            # 创建简单的边界框
            boxes = torch.tensor([[0.2, 0.2, 0.8, 0.8]], dtype=torch.float32)  # 一个框
            labels = torch.tensor([0], dtype=torch.long)  # person类别
            
            targets.append({
                'boxes': boxes,
                'labels': labels
            })
        
        return targets
    
    def test_single_image_selection(self, num_iterations: int = 20):
        """测试单图片选择逻辑"""
        print(f"\n🔍 测试单图片选择逻辑 ({num_iterations} 次迭代)")
        
        batch_size = 4
        
        for global_step in range(num_iterations):
            # 创建测试数据
            images = self.create_test_images(batch_size)
            targets = self.create_test_targets(batch_size)
            
            # 模拟原始逻辑：每个迭代选择不同图片
            img_idx = global_step % batch_size
            selected_img = images[img_idx]
            
            # 记录到TensorBoard
            self.writer.add_image("Test_Single_Image/Training_Progress", 
                                selected_img, global_step)
            
            # 记录选择信息
            self.writer.add_scalar("Test_Info/Selected_Index", img_idx, global_step)
            self.writer.add_scalar("Test_Info/Global_Step", global_step, global_step)
            
            print(f"  迭代 {global_step:2d}: 选择图片 {img_idx+1} (索引 {img_idx})")
        
        print(f"✅ 测试完成！请在TensorBoard中查看 Test_Single_Image/Training_Progress")
        print(f"   应该看到每个步骤显示不同颜色的图片，循环显示")
    
    def test_detection_comparison(self, num_iterations: int = 10):
        """测试检测对比可视化"""
        print(f"\n🎯 测试检测对比可视化 ({num_iterations} 次迭代)")
        
        batch_size = 3
        
        for global_step in range(num_iterations):
            # 创建测试数据
            images = self.create_test_images(batch_size)
            targets = self.create_test_targets(batch_size)
            
            # 选择图片
            img_idx = global_step % batch_size
            img_tensor = images[img_idx]
            
            # 转换为numpy用于绘制
            img_np = (img_tensor.permute(1, 2, 0).numpy() * 255).astype(np.uint8)
            h, w = img_np.shape[:2]
            
            # 创建对比图像
            comparison_img = np.zeros((h, w * 2, 3), dtype=np.uint8)
            comparison_img[:, :w] = img_np.copy()  # 左侧：原图
            comparison_img[:, w:] = img_np.copy()  # 右侧：原图
            
            # 添加标题
            cv2.putText(comparison_img, f"Step {global_step}, Image {img_idx+1}",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(comparison_img, "Ground Truth", (10, h-20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(comparison_img, "Prediction", (w+10, h-20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 绘制测试框
            # 左侧：真实框（绿色）
            cv2.rectangle(comparison_img, (int(0.2*w), int(0.2*h)), 
                         (int(0.8*w), int(0.8*h)), (0, 255, 0), 3)
            
            # 右侧：预测框（红色）
            cv2.rectangle(comparison_img, (int(0.25*w)+w, int(0.25*h)), 
                         (int(0.75*w)+w, int(0.75*h)), (255, 0, 0), 2)
            
            # 转换为tensor并记录
            comparison_tensor = torch.from_numpy(comparison_img).permute(2, 0, 1).float() / 255.0
            self.writer.add_image("Test_Detection_Comparison/Training_Progress", 
                                comparison_tensor, global_step)
            
            print(f"  迭代 {global_step:2d}: 检测对比图片 {img_idx+1}")
        
        print(f"✅ 检测对比测试完成！")
    
    def run_test(self):
        """运行完整测试"""
        print("="*60)
        print("🧪 HVI-RF-DETR 单图片迭代可视化测试")
        print("="*60)
        
        # 测试1：单图片选择
        self.test_single_image_selection(20)
        
        # 测试2：检测对比
        self.test_detection_comparison(15)
        
        # 强制刷新
        self.writer.flush()
        
        print("\n" + "="*60)
        print("🎉 所有测试完成！")
        print(f"📈 启动TensorBoard查看结果:")
        print(f"   tensorboard --logdir {self.test_dir / 'tensorboard'}")
        print(f"   访问: http://localhost:6006")
        print("\n💡 验证要点:")
        print("   1. Test_Single_Image/Training_Progress 应该显示循环的不同颜色图片")
        print("   2. Test_Detection_Comparison/Training_Progress 应该显示检测框对比")
        print("   3. 每个迭代步骤只显示一张图片，不是多张")
        print("   4. 拖动进度条应该看到图片变化")
        print("="*60)
        
        # 关闭writer
        self.writer.close()

def main():
    """主函数"""
    test = SingleImageVisualizationTest()
    test.run_test()

if __name__ == "__main__":
    main()
