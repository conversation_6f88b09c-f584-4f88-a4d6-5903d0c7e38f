#!/usr/bin/env python3
"""
测试显存优化效果
对比优化前后的显存使用情况
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import time
import psutil
from pathlib import Path

def get_memory_info():
    """获取内存和显存信息"""
    memory_info = {}
    
    # CPU内存
    memory_info['cpu_memory_gb'] = psutil.virtual_memory().used / 1024**3
    
    # GPU显存
    if torch.cuda.is_available():
        memory_info['gpu_memory_allocated_gb'] = torch.cuda.memory_allocated() / 1024**3
        memory_info['gpu_memory_reserved_gb'] = torch.cuda.memory_reserved() / 1024**3
        memory_info['gpu_memory_free_gb'] = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_reserved()) / 1024**3
    else:
        memory_info['gpu_memory_allocated_gb'] = 0
        memory_info['gpu_memory_reserved_gb'] = 0
        memory_info['gpu_memory_free_gb'] = 0
    
    return memory_info

def test_memory_usage():
    """测试显存使用情况"""
    print("🧪 测试显存优化效果")
    print("="*60)
    
    try:
        from unified_full_training_28k import UnifiedTrainingConfig, UnifiedTrainer
        
        # 创建测试配置
        config = UnifiedTrainingConfig(
            epochs=1,
            batch_size=8,
            max_samples=16,
            viz_interval=1,  # 每个batch都可视化，测试最大压力
            log_interval=1
        )
        
        print(f"📊 测试配置:")
        print(f"   - Batch Size: {config.batch_size}")
        print(f"   - Max Samples: {config.max_samples}")
        print(f"   - 可视化间隔: 每 {config.viz_interval} 个batch")
        
        # 初始内存状态
        initial_memory = get_memory_info()
        print(f"\n📊 初始内存状态:")
        print(f"   CPU内存: {initial_memory['cpu_memory_gb']:.2f} GB")
        print(f"   GPU分配: {initial_memory['gpu_memory_allocated_gb']:.2f} GB")
        print(f"   GPU保留: {initial_memory['gpu_memory_reserved_gb']:.2f} GB")
        print(f"   GPU可用: {initial_memory['gpu_memory_free_gb']:.2f} GB")
        
        # 创建训练器
        trainer = UnifiedTrainer(config)
        
        after_init_memory = get_memory_info()
        print(f"\n📊 训练器初始化后:")
        print(f"   CPU内存: {after_init_memory['cpu_memory_gb']:.2f} GB (+{after_init_memory['cpu_memory_gb']-initial_memory['cpu_memory_gb']:.2f})")
        print(f"   GPU分配: {after_init_memory['gpu_memory_allocated_gb']:.2f} GB (+{after_init_memory['gpu_memory_allocated_gb']-initial_memory['gpu_memory_allocated_gb']:.2f})")
        print(f"   GPU保留: {after_init_memory['gpu_memory_reserved_gb']:.2f} GB (+{after_init_memory['gpu_memory_reserved_gb']-initial_memory['gpu_memory_reserved_gb']:.2f})")
        
        # 加载数据集
        dataloader, dataset = trainer.load_dataset()
        
        after_data_memory = get_memory_info()
        print(f"\n📊 数据集加载后:")
        print(f"   CPU内存: {after_data_memory['cpu_memory_gb']:.2f} GB (+{after_data_memory['cpu_memory_gb']-after_init_memory['cpu_memory_gb']:.2f})")
        print(f"   GPU分配: {after_data_memory['gpu_memory_allocated_gb']:.2f} GB (+{after_data_memory['gpu_memory_allocated_gb']-after_init_memory['gpu_memory_allocated_gb']:.2f})")
        
        # 创建模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = trainer.create_model().to(device)
        
        after_model_memory = get_memory_info()
        print(f"\n📊 模型创建后:")
        print(f"   CPU内存: {after_model_memory['cpu_memory_gb']:.2f} GB (+{after_model_memory['cpu_memory_gb']-after_data_memory['cpu_memory_gb']:.2f})")
        print(f"   GPU分配: {after_model_memory['gpu_memory_allocated_gb']:.2f} GB (+{after_model_memory['gpu_memory_allocated_gb']-after_data_memory['gpu_memory_allocated_gb']:.2f})")
        print(f"   GPU保留: {after_model_memory['gpu_memory_reserved_gb']:.2f} GB (+{after_model_memory['gpu_memory_reserved_gb']-after_data_memory['gpu_memory_reserved_gb']:.2f})")
        
        # 测试可视化内存使用
        print(f"\n🖼️ 测试可视化显存使用...")
        
        memory_before_viz = []
        memory_after_viz = []
        
        epoch = 0
        total_batches = len(dataloader)
        
        for batch_idx, (images, targets) in enumerate(dataloader):
            # 记录可视化前的内存
            before_viz = get_memory_info()
            memory_before_viz.append(before_viz['gpu_memory_allocated_gb'])
            
            images = images.to(device)
            global_step = epoch * total_batches + batch_idx
            
            # 执行可视化
            trainer._log_detection_visualizations(
                images, targets, epoch, batch_idx, global_step, total_batches
            )
            
            # 记录可视化后的内存
            after_viz = get_memory_info()
            memory_after_viz.append(after_viz['gpu_memory_allocated_gb'])
            
            print(f"   批次 {batch_idx}: 可视化前 {before_viz['gpu_memory_allocated_gb']:.2f} GB -> 可视化后 {after_viz['gpu_memory_allocated_gb']:.2f} GB (增加 {after_viz['gpu_memory_allocated_gb']-before_viz['gpu_memory_allocated_gb']:.2f} GB)")
            
            # 只测试前2个batch
            if batch_idx >= 1:
                break
        
        # 分析内存使用
        avg_memory_increase = np.mean([after - before for before, after in zip(memory_before_viz, memory_after_viz)])
        max_memory_increase = max([after - before for before, after in zip(memory_before_viz, memory_after_viz)])
        
        print(f"\n📊 可视化内存分析:")
        print(f"   平均增加: {avg_memory_increase:.3f} GB")
        print(f"   最大增加: {max_memory_increase:.3f} GB")
        
        # 最终内存状态
        final_memory = get_memory_info()
        print(f"\n📊 最终内存状态:")
        print(f"   CPU内存: {final_memory['cpu_memory_gb']:.2f} GB (总增加 {final_memory['cpu_memory_gb']-initial_memory['cpu_memory_gb']:.2f})")
        print(f"   GPU分配: {final_memory['gpu_memory_allocated_gb']:.2f} GB (总增加 {final_memory['gpu_memory_allocated_gb']-initial_memory['gpu_memory_allocated_gb']:.2f})")
        print(f"   GPU保留: {final_memory['gpu_memory_reserved_gb']:.2f} GB (总增加 {final_memory['gpu_memory_reserved_gb']-initial_memory['gpu_memory_reserved_gb']:.2f})")
        
        # 评估优化效果
        print(f"\n💡 优化效果评估:")
        if avg_memory_increase < 0.1:
            print(f"   ✅ 优秀：可视化显存增加很少 ({avg_memory_increase:.3f} GB)")
        elif avg_memory_increase < 0.5:
            print(f"   ✅ 良好：可视化显存增加适中 ({avg_memory_increase:.3f} GB)")
        else:
            print(f"   ⚠️ 需要优化：可视化显存增加较多 ({avg_memory_increase:.3f} GB)")
        
        # 强制刷新和清理
        trainer.writer.flush()
        trainer.writer.close()
        
        # 清理显存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 显存优化测试")
    print("="*60)
    
    success = test_memory_usage()
    
    if success:
        print(f"\n🎉 显存优化测试完成!")
        print(f"\n📋 优化措施总结:")
        print(f"   1. ✅ 使用torch.no_grad()避免梯度计算")
        print(f"   2. ✅ 立即将图片移到CPU释放GPU显存")
        print(f"   3. ✅ 使用detach()断开梯度连接")
        print(f"   4. ✅ 显式删除tensor释放内存")
        print(f"   5. ✅ 逐个处理图片避免同时占用")
        print(f"   6. ✅ 定期调用torch.cuda.empty_cache()")
        print(f"   7. ✅ 减少可视化频率(50个batch)")
    else:
        print(f"\n❌ 显存优化测试失败")
    
    print("="*60)

if __name__ == "__main__":
    main()
