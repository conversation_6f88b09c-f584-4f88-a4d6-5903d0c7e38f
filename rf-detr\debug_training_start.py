#!/usr/bin/env python3
"""
调试训练启动问题
逐步检查训练系统的各个组件
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import time
from pathlib import Path

def check_environment():
    """检查环境"""
    print("🔍 检查训练环境...")
    print(f"   Python版本: {sys.version}")
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   CUDA设备: {torch.cuda.get_device_name()}")
        print(f"   CUDA内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    print()

def check_data_paths():
    """检查数据路径"""
    print("📁 检查数据路径...")
    
    data_root = Path("data/night")
    images_dir = data_root / "images" / "100k" / "train"
    annotations_file = data_root / "annotations" / "bdd100k_night_coco.json"
    
    print(f"   数据根目录: {data_root} {'✅' if data_root.exists() else '❌'}")
    print(f"   图片目录: {images_dir} {'✅' if images_dir.exists() else '❌'}")
    print(f"   标注文件: {annotations_file} {'✅' if annotations_file.exists() else '❌'}")
    
    if images_dir.exists():
        image_count = len(list(images_dir.glob("*.jpg")))
        print(f"   图片数量: {image_count}")
    print()

def test_dataset_creation():
    """测试数据集创建"""
    print("📊 测试数据集创建...")
    
    try:
        from datasets.bdd100k_night_dataset import create_bdd100k_dataloader
        
        print("   正在创建数据加载器...")
        dataloader, dataset = create_bdd100k_dataloader(
            data_root="data/night",
            split="train",
            batch_size=2,
            max_samples=10,  # 只用10张图片测试
            shuffle=True
        )
        
        print(f"   ✅ 数据集创建成功，大小: {len(dataset)}")
        
        # 测试加载一个批次
        print("   测试批次加载...")
        for images, targets in dataloader:
            print(f"   ✅ 批次加载成功，形状: {images.shape}")
            break
            
        return True
        
    except Exception as e:
        print(f"   ❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("🤖 测试模型创建...")
    
    try:
        from unified_full_training_28k import UnifiedTrainingConfig, UnifiedTrainer
        
        # 创建简单配置
        config = UnifiedTrainingConfig(
            epochs=1,
            batch_size=2,
            max_samples=5
        )
        
        print("   正在创建训练器...")
        trainer = UnifiedTrainer(config)
        
        print("   正在创建模型...")
        model = trainer.create_model()
        
        print(f"   ✅ 模型创建成功")
        
        # 测试模型前向传播
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        
        test_input = torch.randn(1, 3, 512, 512).to(device)
        print("   测试模型前向传播...")
        
        with torch.no_grad():
            output = model(test_input)
            print(f"   ✅ 前向传播成功，输出形状: {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_step():
    """测试训练步骤"""
    print("🏃 测试训练步骤...")
    
    try:
        from unified_full_training_28k import UnifiedTrainingConfig, UnifiedTrainer
        
        # 创建最小配置
        config = UnifiedTrainingConfig(
            epochs=1,
            batch_size=2,
            max_samples=5,
            log_interval=1,
            viz_interval=1
        )
        
        print("   正在初始化训练器...")
        trainer = UnifiedTrainer(config)
        
        print("   正在加载数据集...")
        dataloader, dataset = trainer.load_dataset()
        
        print("   正在创建模型...")
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = trainer.create_model().to(device)
        
        print("   正在创建优化器...")
        optimizer = torch.optim.AdamW(model.parameters(), lr=config.learning_rate)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=config.epochs)
        
        print("   测试一个训练步骤...")
        model.train()
        
        for batch_idx, (images, targets) in enumerate(dataloader):
            images = images.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            
            # 模拟损失计算
            loss = torch.tensor(1.0, requires_grad=True)
            loss.backward()
            optimizer.step()
            
            print(f"   ✅ 训练步骤成功，批次: {batch_idx}, 损失: {loss.item():.4f}")
            break
        
        return True
        
    except Exception as e:
        print(f"   ❌ 训练步骤失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 HVI-RF-DETR 训练系统调试")
    print("="*60)
    
    # 检查环境
    check_environment()
    
    # 检查数据路径
    check_data_paths()
    
    # 测试数据集创建
    dataset_ok = test_dataset_creation()
    if not dataset_ok:
        print("❌ 数据集测试失败，停止调试")
        return
    
    # 测试模型创建
    model_ok = test_model_creation()
    if not model_ok:
        print("❌ 模型测试失败，停止调试")
        return
    
    # 测试训练步骤
    training_ok = test_training_step()
    if not training_ok:
        print("❌ 训练步骤测试失败")
        return
    
    print("\n🎉 所有组件测试通过!")
    print("✅ 可以开始正式训练")
    print("="*60)

if __name__ == "__main__":
    main()
