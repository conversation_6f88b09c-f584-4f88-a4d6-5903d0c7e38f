2025-06-23T12:16:40.009Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-23T12:16:40.012Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-23T12:16:40.010Z [MCPServer] INFO: Parsed options: {}
2025-06-23T12:16:40.010Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-23T12:16:40.011Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-23T12:16:40.011Z [MCPServer] INFO: Current options: {}
2025-06-23T12:16:40.011Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-23T12:16:40.014Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-23T12:16:40.014Z [MCPServer] INFO: Starting relay server...
2025-06-23T12:16:40.014Z [RelayServerManager] INFO: Starting relay server process
2025-06-23T12:16:40.424Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.422Z [config] INFO: Loaded configuration from environment variables

2025-06-23T12:16:40.424Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.424Z [config] INFO: Configuration initialized

2025-06-23T12:16:40.433Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.433Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-23T12:16:40.434Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.433Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-23T12:16:40.496Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.496Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-23T12:16:40.497Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.496Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-23T12:16:40.497Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.496Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-23T12:16:40.497Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.497Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-23T12:16:40.497Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.497Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-23T12:16:40.498Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.497Z [OscClient] INFO: OSC-MSG-mc929lblpq3: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-23T12:16:40.501Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.500Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-23T12:16:40.506Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.506Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-23T12:16:40.507Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.506Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-23T12:16:40.508Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:40.508Z [OscClient] INFO: OSC-MSG-mc929lblpq3: Successfully sent

2025-06-23T12:16:40.422Z [config] INFO: Loaded configuration from environment variables
2025-06-23T12:16:40.433Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-23T12:16:40.497Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-23T12:16:40.496Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-23T12:16:40.424Z [config] INFO: Configuration initialized
2025-06-23T12:16:40.433Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-23T12:16:40.496Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-23T12:16:40.496Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-23T12:16:40.497Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-23T12:16:40.497Z [OscClient] INFO: OSC-MSG-mc929lblpq3: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-23T12:16:40.500Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-23T12:16:40.508Z [OscClient] INFO: OSC-MSG-mc929lblpq3: Successfully sent
2025-06-23T12:16:40.506Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-23T12:16:40.506Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-23T12:16:41.240Z [RelayServerManager] INFO: Relay server started successfully
2025-06-23T12:16:41.241Z [MCPServer] INFO: Relay server started successfully
2025-06-23T12:16:41.241Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-23T12:16:41.241Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-23T12:16:41.241Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-23T12:16:41.260Z [WebSocketServer] INFO: New WebSocket connection
2025-06-23T12:16:41.261Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-23T12:16:41.263Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:16:41.260Z [WebSocketServer] INFO: New WebSocket connection

2025-06-23T12:16:41.261Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-23T12:16:41.263Z [WebSocketClient] INFO: Connection test successful
2025-06-23T12:16:41.263Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-23T12:16:41.264Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-23T12:20:48.572Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-23T12:20:48.575Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-23T12:20:48.573Z [MCPServer] INFO: Parsed options: {}
2025-06-23T12:20:48.574Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-23T12:20:48.574Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-23T12:20:48.574Z [MCPServer] INFO: Current options: {}
2025-06-23T12:20:48.574Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-23T12:20:48.577Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-23T12:20:48.578Z [MCPServer] INFO: Starting relay server...
2025-06-23T12:20:48.578Z [RelayServerManager] INFO: Starting relay server process
2025-06-23T12:20:49.033Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.032Z [config] INFO: Loaded configuration from environment variables

2025-06-23T12:20:49.034Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.033Z [config] INFO: Configuration initialized

2025-06-23T12:20:49.044Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.044Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-23T12:20:49.045Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.044Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-23T12:20:49.110Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.109Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-23T12:20:49.110Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.110Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-23T12:20:49.110Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.110Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-23T12:20:49.110Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.110Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-23T12:20:49.111Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.110Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-23T12:20:49.111Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.110Z [OscClient] INFO: OSC-MSG-mc92ex5iq8o: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-23T12:20:49.113Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.113Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-23T12:20:49.117Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.117Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-23T12:20:49.117Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.117Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-23T12:20:49.119Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.119Z [OscClient] INFO: OSC-MSG-mc92ex5iq8o: Successfully sent

2025-06-23T12:20:49.032Z [config] INFO: Loaded configuration from environment variables
2025-06-23T12:20:49.044Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-23T12:20:49.110Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-23T12:20:49.110Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-23T12:20:49.033Z [config] INFO: Configuration initialized
2025-06-23T12:20:49.044Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-23T12:20:49.109Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-23T12:20:49.110Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-23T12:20:49.110Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-23T12:20:49.110Z [OscClient] INFO: OSC-MSG-mc92ex5iq8o: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-23T12:20:49.113Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-23T12:20:49.119Z [OscClient] INFO: OSC-MSG-mc92ex5iq8o: Successfully sent
2025-06-23T12:20:49.117Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-23T12:20:49.117Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-23T12:20:49.828Z [RelayServerManager] INFO: Relay server started successfully
2025-06-23T12:20:49.829Z [MCPServer] INFO: Relay server started successfully
2025-06-23T12:20:49.829Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-23T12:20:49.829Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-23T12:20:49.829Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-23T12:20:49.848Z [WebSocketServer] INFO: New WebSocket connection
2025-06-23T12:20:49.850Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-23T12:20:49.852Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:20:49.848Z [WebSocketServer] INFO: New WebSocket connection

2025-06-23T12:20:49.850Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-23T12:20:49.852Z [WebSocketClient] INFO: Connection test successful
2025-06-23T12:20:49.853Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-23T12:20:49.853Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-23T12:21:20.683Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-23T12:21:20.686Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-23T12:21:20.684Z [MCPServer] INFO: Parsed options: {}
2025-06-23T12:21:20.684Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-23T12:21:20.685Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-23T12:21:20.685Z [MCPServer] INFO: Current options: {}
2025-06-23T12:21:20.685Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-23T12:21:20.689Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-23T12:21:20.689Z [MCPServer] INFO: Starting relay server...
2025-06-23T12:21:20.689Z [RelayServerManager] INFO: Starting relay server process
2025-06-23T12:21:21.090Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.089Z [config] INFO: Loaded configuration from environment variables

2025-06-23T12:21:21.090Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.090Z [config] INFO: Configuration initialized

2025-06-23T12:21:21.100Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.100Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-23T12:21:21.100Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.100Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-23T12:21:21.162Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.162Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-23T12:21:21.163Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.162Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-23T12:21:21.163Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.163Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-23T12:21:21.163Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.163Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-23T12:21:21.163Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.163Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-23T12:21:21.163Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.163Z [OscClient] INFO: OSC-MSG-mc92flvvdsi: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-23T12:21:21.165Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.166Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-23T12:21:21.169Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.169Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-23T12:21:21.170Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.170Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-23T12:21:21.171Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.172Z [OscClient] INFO: OSC-MSG-mc92flvvdsi: Successfully sent

2025-06-23T12:21:21.089Z [config] INFO: Loaded configuration from environment variables
2025-06-23T12:21:21.100Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-23T12:21:21.163Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-23T12:21:21.163Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-23T12:21:21.090Z [config] INFO: Configuration initialized
2025-06-23T12:21:21.100Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-23T12:21:21.162Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-23T12:21:21.162Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-23T12:21:21.163Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-23T12:21:21.163Z [OscClient] INFO: OSC-MSG-mc92flvvdsi: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-23T12:21:21.166Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-23T12:21:21.172Z [OscClient] INFO: OSC-MSG-mc92flvvdsi: Successfully sent
2025-06-23T12:21:21.169Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-23T12:21:21.170Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-23T12:21:21.906Z [RelayServerManager] INFO: Relay server started successfully
2025-06-23T12:21:21.906Z [MCPServer] INFO: Relay server started successfully
2025-06-23T12:21:21.906Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-23T12:21:21.906Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-23T12:21:21.906Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-23T12:21:21.927Z [WebSocketServer] INFO: New WebSocket connection
2025-06-23T12:21:21.928Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-23T12:21:21.930Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:21:21.927Z [WebSocketServer] INFO: New WebSocket connection

2025-06-23T12:21:21.928Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-23T12:21:21.931Z [WebSocketClient] INFO: Connection test successful
2025-06-23T12:21:21.931Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-23T12:21:21.931Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-23T12:23:41.936Z [OscClient] INFO: Avatar changed: avtr_3afefb0b-f3fd-436a-bd3d-b6a14ec62077
2025-06-23T12:23:41.936Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:41.936Z [OscClient] INFO: Avatar changed: avtr_3afefb0b-f3fd-436a-bd3d-b6a14ec62077

2025-06-23T12:23:41.937Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:41.936Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-23T12:23:41.937Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:41.936Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-23T12:23:42.011Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:42.010Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-23T12:23:42.011Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:42.011Z [OscClient] INFO: Found avatar name in configs: rusk M․O․O․N

2025-06-23T12:23:42.011Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:42.011Z [OscClient] INFO: Initialized 37 parameters from config

2025-06-23T12:23:42.011Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:42.011Z [OscClient] INFO: Initialized parameter values for new avatar with 37 parameters

2025-06-23T12:23:42.011Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:42.011Z [OscClient] ERROR: [object Object]

2025-06-23T12:23:42.011Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:23:42.011Z [OscClient] ERROR: [object Map]

2025-06-23T12:23:41.936Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-23T12:23:41.936Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-23T12:23:42.010Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-23T12:23:42.011Z [OscClient] INFO: Found avatar name in configs: rusk M․O․O․N
2025-06-23T12:23:42.011Z [OscClient] INFO: Initialized 37 parameters from config
2025-06-23T12:23:42.011Z [OscClient] INFO: Initialized parameter values for new avatar with 37 parameters
2025-06-23T12:23:42.011Z [OscClient] ERROR: [object Object]
2025-06-23T12:23:42.011Z [OscClient] ERROR: [object Map]
2025-06-23T12:41:56.516Z [OscClient] INFO: Avatar changed: avtr_3afefb0b-f3fd-436a-bd3d-b6a14ec62077
2025-06-23T12:41:56.516Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.516Z [OscClient] INFO: Avatar changed: avtr_3afefb0b-f3fd-436a-bd3d-b6a14ec62077

2025-06-23T12:41:56.517Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.517Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-23T12:41:56.517Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.517Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-23T12:41:56.623Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.623Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-23T12:41:56.623Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.623Z [OscClient] INFO: Found avatar name in configs: rusk M․O․O․N

2025-06-23T12:41:56.623Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.623Z [OscClient] INFO: Initialized 37 parameters from config

2025-06-23T12:41:56.623Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.623Z [OscClient] INFO: Initialized parameter values for new avatar with 37 parameters

2025-06-23T12:41:56.623Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.623Z [OscClient] ERROR: [object Object]

2025-06-23T12:41:56.623Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T12:41:56.623Z [OscClient] ERROR: [object Map]

2025-06-23T12:41:56.517Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-23T12:41:56.517Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-23T12:41:56.623Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-23T12:41:56.623Z [OscClient] INFO: Found avatar name in configs: rusk M․O․O․N
2025-06-23T12:41:56.623Z [OscClient] INFO: Initialized 37 parameters from config
2025-06-23T12:41:56.623Z [OscClient] INFO: Initialized parameter values for new avatar with 37 parameters
2025-06-23T12:41:56.623Z [OscClient] ERROR: [object Object]
2025-06-23T12:41:56.623Z [OscClient] ERROR: [object Map]
2025-06-23T15:34:27.482Z [WebSocketServer] INFO: New WebSocket connection
2025-06-23T15:34:27.482Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:34:27.482Z [WebSocketServer] INFO: New WebSocket connection

2025-06-23T15:37:50.354Z [WebSocketServer] INFO: WebSocket connection closed
2025-06-23T15:37:50.358Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:37:50.354Z [WebSocketServer] INFO: WebSocket connection closed

2025-06-23T15:39:11.947Z [WebSocketServer] INFO: New WebSocket connection
2025-06-23T15:39:11.947Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:39:11.947Z [WebSocketServer] INFO: New WebSocket connection

2025-06-23T15:39:35.669Z [WebSocketServer] INFO: WebSocket connection closed
2025-06-23T15:39:35.670Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:39:35.669Z [WebSocketServer] INFO: WebSocket connection closed

2025-06-23T15:40:12.829Z [WebSocketServer] INFO: New WebSocket connection
2025-06-23T15:40:12.829Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:40:12.829Z [WebSocketServer] INFO: New WebSocket connection

2025-06-23T15:40:15.686Z [WebSocketServer] INFO: WebSocket connection closed
2025-06-23T15:40:15.686Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:40:15.686Z [WebSocketServer] INFO: WebSocket connection closed

2025-06-23T15:41:42.950Z [WebSocketServer] INFO: New WebSocket connection
2025-06-23T15:41:42.950Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:41:42.950Z [WebSocketServer] INFO: New WebSocket connection

2025-06-23T15:46:29.341Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-23T15:46:29.344Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-23T15:46:29.343Z [MCPServer] INFO: Parsed options: {}
2025-06-23T15:46:29.343Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-23T15:46:29.343Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-23T15:46:29.343Z [MCPServer] INFO: Current options: {}
2025-06-23T15:46:29.344Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-23T15:46:29.347Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-23T15:46:29.347Z [MCPServer] INFO: Starting relay server...
2025-06-23T15:46:29.347Z [RelayServerManager] INFO: Starting relay server process
2025-06-23T15:46:29.922Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.921Z [config] INFO: Loaded configuration from environment variables

2025-06-23T15:46:29.923Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.923Z [config] INFO: Configuration initialized

2025-06-23T15:46:29.930Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.930Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-23T15:46:29.931Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.930Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-23T15:46:29.986Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.986Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-23T15:46:29.986Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.986Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-23T15:46:29.987Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.986Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-23T15:46:29.987Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.987Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-23T15:46:29.987Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.987Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-23T15:46:29.987Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.987Z [OscClient] INFO: OSC-MSG-mc99rffnafp: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-23T15:46:29.989Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.989Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-23T15:46:29.993Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.993Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-23T15:46:29.993Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.993Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-23T15:46:29.995Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:29.995Z [OscClient] INFO: OSC-MSG-mc99rffnafp: Successfully sent

2025-06-23T15:46:29.921Z [config] INFO: Loaded configuration from environment variables
2025-06-23T15:46:29.930Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-23T15:46:29.987Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-23T15:46:29.986Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-23T15:46:29.923Z [config] INFO: Configuration initialized
2025-06-23T15:46:29.930Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-23T15:46:29.986Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-23T15:46:29.986Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-23T15:46:29.987Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-23T15:46:29.987Z [OscClient] INFO: OSC-MSG-mc99rffnafp: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-23T15:46:29.989Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-23T15:46:29.995Z [OscClient] INFO: OSC-MSG-mc99rffnafp: Successfully sent
2025-06-23T15:46:29.993Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-23T15:46:29.993Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-23T15:46:30.596Z [RelayServerManager] INFO: Relay server started successfully
2025-06-23T15:46:30.596Z [MCPServer] INFO: Relay server started successfully
2025-06-23T15:46:30.596Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-23T15:46:30.596Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-23T15:46:30.597Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-23T15:46:30.614Z [WebSocketServer] INFO: New WebSocket connection
2025-06-23T15:46:30.615Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-23T15:46:30.617Z [RelayServerManager] ERROR: [RelayServer] 2025-06-23T15:46:30.614Z [WebSocketServer] INFO: New WebSocket connection

2025-06-23T15:46:30.615Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-23T15:46:30.617Z [WebSocketClient] INFO: Connection test successful
2025-06-23T15:46:30.617Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-23T15:46:30.618Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-24T01:29:55.488Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-24T01:29:55.492Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-24T01:29:55.490Z [MCPServer] INFO: Parsed options: {}
2025-06-24T01:29:55.490Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-24T01:29:55.491Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-24T01:29:55.491Z [MCPServer] INFO: Current options: {}
2025-06-24T01:29:55.491Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-24T01:29:55.494Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-24T01:29:55.494Z [MCPServer] INFO: Starting relay server...
2025-06-24T01:29:55.495Z [RelayServerManager] INFO: Starting relay server process
2025-06-24T01:29:56.054Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:56.053Z [config] INFO: Loaded configuration from environment variables

2025-06-24T01:29:56.055Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:56.054Z [config] INFO: Configuration initialized

2025-06-24T01:29:56.062Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:56.062Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-24T01:29:56.063Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:56.063Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-24T01:29:56.778Z [RelayServerManager] INFO: Relay server started successfully
2025-06-24T01:29:56.778Z [MCPServer] INFO: Relay server started successfully
2025-06-24T01:29:56.778Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-24T01:29:56.778Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-24T01:29:56.778Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-24T01:29:56.794Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-24T01:29:56.794Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-24T01:29:56.794Z [WebSocketClient] WARN: Connection attempt 1 failed: 
2025-06-24T01:29:56.794Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-24T01:29:57.746Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.746Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-24T01:29:57.746Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.746Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-24T01:29:57.747Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.746Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-24T01:29:57.747Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.747Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-24T01:29:57.747Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.747Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-24T01:29:57.747Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.747Z [OscClient] INFO: OSC-MSG-mc9ulrlfduc: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-24T01:29:57.749Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.749Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-24T01:29:57.752Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.752Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-24T01:29:57.752Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.752Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-24T01:29:57.753Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:57.753Z [OscClient] INFO: OSC-MSG-mc9ulrlfduc: Successfully sent

2025-06-24T01:29:56.053Z [config] INFO: Loaded configuration from environment variables
2025-06-24T01:29:56.062Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-24T01:29:57.747Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-24T01:29:57.746Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-24T01:29:56.054Z [config] INFO: Configuration initialized
2025-06-24T01:29:56.063Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-24T01:29:57.746Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-24T01:29:57.746Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-24T01:29:57.747Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-24T01:29:57.747Z [OscClient] INFO: OSC-MSG-mc9ulrlfduc: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-24T01:29:57.749Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-24T01:29:57.753Z [OscClient] INFO: OSC-MSG-mc9ulrlfduc: Successfully sent
2025-06-24T01:29:57.752Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-24T01:29:57.752Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-24T01:29:58.808Z [WebSocketClient] INFO: Connection attempt 2 of 3
2025-06-24T01:29:58.812Z [WebSocketServer] INFO: New WebSocket connection
2025-06-24T01:29:58.813Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-24T01:29:58.815Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T01:29:58.812Z [WebSocketServer] INFO: New WebSocket connection

2025-06-24T01:29:58.813Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-24T01:29:58.815Z [WebSocketClient] INFO: Connection test successful
2025-06-24T01:29:58.815Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-24T01:29:58.816Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-24T03:49:57.260Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-24T03:49:57.263Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-24T03:49:57.261Z [MCPServer] INFO: Parsed options: {}
2025-06-24T03:49:57.262Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-24T03:49:57.262Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-24T03:49:57.262Z [MCPServer] INFO: Current options: {}
2025-06-24T03:49:57.262Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-24T03:49:57.265Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-24T03:49:57.265Z [MCPServer] INFO: Starting relay server...
2025-06-24T03:49:57.265Z [RelayServerManager] INFO: Starting relay server process
2025-06-24T03:49:58.025Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:49:58.024Z [config] INFO: Loaded configuration from environment variables

2025-06-24T03:49:58.025Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:49:58.025Z [config] INFO: Configuration initialized

2025-06-24T03:49:58.034Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:49:58.034Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-24T03:49:58.035Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:49:58.035Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-24T03:49:58.637Z [RelayServerManager] INFO: Relay server started successfully
2025-06-24T03:49:58.638Z [MCPServer] INFO: Relay server started successfully
2025-06-24T03:49:58.638Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-24T03:49:58.638Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-24T03:49:58.638Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-24T03:49:58.657Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-24T03:49:58.657Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-24T03:49:58.657Z [WebSocketClient] WARN: Connection attempt 1 failed: 
2025-06-24T03:49:58.657Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-24T03:50:00.658Z [WebSocketClient] INFO: Connection attempt 2 of 3
2025-06-24T03:50:00.660Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-24T03:50:00.660Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-24T03:50:00.660Z [WebSocketClient] WARN: Connection attempt 2 failed: 
2025-06-24T03:50:00.660Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-24T03:50:01.463Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.463Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-24T03:50:01.464Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.463Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-24T03:50:01.464Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.463Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-24T03:50:01.464Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.464Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-24T03:50:01.464Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.464Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-24T03:50:01.464Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.464Z [OscClient] INFO: OSC-MSG-mc9zlvy0s9e: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-24T03:50:01.468Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.468Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-24T03:50:01.471Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.471Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-24T03:50:01.471Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.471Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-24T03:50:01.473Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:01.473Z [OscClient] INFO: OSC-MSG-mc9zlvy0s9e: Successfully sent

2025-06-24T03:49:58.024Z [config] INFO: Loaded configuration from environment variables
2025-06-24T03:49:58.034Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-24T03:50:01.464Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-24T03:50:01.463Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-24T03:49:58.025Z [config] INFO: Configuration initialized
2025-06-24T03:49:58.035Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-24T03:50:01.463Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-24T03:50:01.463Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-24T03:50:01.464Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-24T03:50:01.464Z [OscClient] INFO: OSC-MSG-mc9zlvy0s9e: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-24T03:50:01.468Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-24T03:50:01.473Z [OscClient] INFO: OSC-MSG-mc9zlvy0s9e: Successfully sent
2025-06-24T03:50:01.471Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-24T03:50:01.471Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-24T03:50:02.665Z [WebSocketClient] INFO: Connection attempt 3 of 3
2025-06-24T03:50:02.669Z [WebSocketServer] INFO: New WebSocket connection
2025-06-24T03:50:02.670Z [WebSocketClient] INFO: Connected to WebSocket server
2025-06-24T03:50:02.671Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T03:50:02.669Z [WebSocketServer] INFO: New WebSocket connection

2025-06-24T03:50:02.670Z [WebSocketClient] INFO: Testing connection (attempt 1 of 3)
2025-06-24T03:50:02.672Z [WebSocketClient] INFO: Connection test successful
2025-06-24T03:50:02.672Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-24T03:50:02.672Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-24T05:57:09.769Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-24T05:57:09.772Z [RelayServerManager] INFO: RelayServerManager initialized
2025-06-24T05:57:09.771Z [MCPServer] INFO: Parsed options: {}
2025-06-24T05:57:09.771Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-24T05:57:09.771Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-24T05:57:09.771Z [MCPServer] INFO: Current options: {}
2025-06-24T05:57:09.771Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-24T05:57:09.774Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-24T05:57:09.774Z [MCPServer] INFO: Starting relay server...
2025-06-24T05:57:09.774Z [RelayServerManager] INFO: Starting relay server process
2025-06-24T05:57:10.412Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:10.410Z [config] INFO: Loaded configuration from environment variables

2025-06-24T05:57:10.412Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:10.411Z [config] INFO: Configuration initialized

2025-06-24T05:57:10.419Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:10.418Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC

2025-06-24T05:57:10.419Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:10.418Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7

2025-06-24T05:57:10.959Z [RelayServerManager] INFO: Relay server started successfully
2025-06-24T05:57:10.960Z [MCPServer] INFO: Relay server started successfully
2025-06-24T05:57:10.960Z [WebSocketClient] INFO: Connecting to WebSocket server at ws://localhost:8765
2025-06-24T05:57:10.960Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-24T05:57:10.960Z [WebSocketClient] INFO: Connection attempt 1 of 3
2025-06-24T05:57:10.974Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-24T05:57:10.974Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-24T05:57:10.974Z [WebSocketClient] WARN: Connection attempt 1 failed: 
2025-06-24T05:57:10.974Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-24T05:57:12.983Z [WebSocketClient] INFO: Connection attempt 2 of 3
2025-06-24T05:57:12.984Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-24T05:57:12.984Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-24T05:57:12.984Z [WebSocketClient] WARN: Connection attempt 2 failed: 
2025-06-24T05:57:12.984Z [WebSocketClient] INFO: Retrying in 2 seconds...
2025-06-24T05:57:14.991Z [WebSocketClient] INFO: Connection attempt 3 of 3
2025-06-24T05:57:14.993Z [WebSocketClient] ERROR: WebSocket error: 
2025-06-24T05:57:14.993Z [MCPServer] WARN: Failed to connect to WebSocket server. Some features may not work.
2025-06-24T05:57:14.993Z [WebSocketClient] INFO: WebSocket connection closed
2025-06-24T05:57:14.993Z [WebSocketClient] WARN: Connection attempt 3 failed: 
2025-06-24T05:57:14.993Z [WebSocketClient] ERROR: Failed to connect after 3 attempts
2025-06-24T05:57:14.993Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-24T05:57:14.993Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-24T05:57:15.194Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.193Z [OscClient] INFO: Loaded 195 avatar configurations

2025-06-24T05:57:15.194Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.193Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)

2025-06-24T05:57:15.194Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.193Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-24T05:57:15.194Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.193Z [RelayServer] INFO: Starting VRChat OSC relay server

2025-06-24T05:57:15.194Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.194Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]

2025-06-24T05:57:15.194Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.194Z [OscClient] INFO: OSC-MSG-mca45i62uqn: Sending message to /chatbox/input with value ["OSC client connected", true, true]

2025-06-24T05:57:15.196Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.196Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001

2025-06-24T05:57:15.199Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.198Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-24T05:57:15.199Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.199Z [RelayServer] INFO: VRChat OSC relay server started successfully

2025-06-24T05:57:15.201Z [RelayServerManager] ERROR: [RelayServer] 2025-06-24T05:57:15.200Z [OscClient] INFO: OSC-MSG-mca45i62uqn: Successfully sent

2025-06-24T05:57:10.410Z [config] INFO: Loaded configuration from environment variables
2025-06-24T05:57:10.418Z [OscClient] INFO: Looking for avatar configs in C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC
2025-06-24T05:57:15.193Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-24T05:57:15.193Z [RelayServer] INFO: Starting VRChat OSC relay server
2025-06-24T05:57:10.411Z [config] INFO: Configuration initialized
2025-06-24T05:57:10.418Z [OscClient] INFO: Checking C:\Users\<USER>\AppData\LocalLow\VRChat\VRChat\OSC\usr_de7629b2-3d57-4e93-8977-ba91307b49c7
2025-06-24T05:57:15.193Z [OscClient] INFO: Loaded 195 avatar configurations
2025-06-24T05:57:15.193Z [OscClient] INFO: OSC client created (send: 127.0.0.1:9000, receive: 127.0.0.1:9001)
2025-06-24T05:57:15.194Z [OscClient] INFO: SENDING OSC: /chatbox/input ["OSC client connected", true, true]
2025-06-24T05:57:15.194Z [OscClient] INFO: OSC-MSG-mca45i62uqn: Sending message to /chatbox/input with value ["OSC client connected", true, true]
2025-06-24T05:57:15.196Z [OscClient] INFO: OSC server listening on 127.0.0.1:9001
2025-06-24T05:57:15.200Z [OscClient] INFO: OSC-MSG-mca45i62uqn: Successfully sent
2025-06-24T05:57:15.198Z [RelayServer] INFO: VRChat OSC relay server started successfully
2025-06-24T05:57:15.199Z [RelayServer] INFO: VRChat OSC relay server started successfully
