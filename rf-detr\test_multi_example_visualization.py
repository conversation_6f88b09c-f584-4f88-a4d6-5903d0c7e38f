#!/usr/bin/env python3
"""
测试多示例图片可视化修复效果
验证TensorBoard从step 0开始显示多个示例板块
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import time
from pathlib import Path
from torch.utils.tensorboard import SummaryWriter
import cv2

def test_multi_example_visualization():
    """测试多示例可视化"""
    print("🧪 测试多示例图片可视化 - 从step 0开始")
    print("="*60)
    
    try:
        from unified_full_training_28k import UnifiedTrainingConfig, UnifiedTrainer
        
        # 创建测试配置
        config = UnifiedTrainingConfig(
            epochs=1,
            batch_size=8,  # 使用8张图片的batch
            max_samples=16,  # 只用16张图片测试
            viz_interval=1,  # 每个batch都可视化
            log_interval=1
        )
        
        print(f"📊 测试配置:")
        print(f"   - Batch Size: {config.batch_size}")
        print(f"   - Max Samples: {config.max_samples}")
        print(f"   - 可视化间隔: 每 {config.viz_interval} 个batch")
        
        # 创建训练器
        trainer = UnifiedTrainer(config)
        
        print(f"\n🚀 开始测试...")
        print(f"📁 实验目录: {trainer.experiment_dir}")
        
        # 加载数据集
        dataloader, dataset = trainer.load_dataset()
        print(f"📊 数据集大小: {len(dataset)}")
        
        # 创建模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = trainer.create_model().to(device)
        
        # 测试前几个batch的可视化
        print(f"\n🖼️ 测试多示例可视化...")
        
        epoch = 0
        total_batches = len(dataloader)
        
        for batch_idx, (images, targets) in enumerate(dataloader):
            print(f"   批次 {batch_idx}: 图片形状 {images.shape}")
            
            # 计算global_step
            global_step = epoch * total_batches + batch_idx
            
            # 调用可视化方法
            trainer._log_detection_visualizations(
                images, targets, epoch, batch_idx, global_step, total_batches
            )
            
            print(f"     ✅ Global Step {global_step}: 已记录 {min(4, images.size(0))} 个示例")
            
            # 只测试前3个batch
            if batch_idx >= 2:
                break
        
        # 强制刷新
        trainer.writer.flush()
        
        print(f"\n✅ 多示例可视化测试完成!")
        print(f"📈 启动TensorBoard查看结果:")
        print(f"   tensorboard --logdir {trainer.experiment_dir / 'tensorboard'}")
        print(f"   访问: http://localhost:6006")
        print(f"\n💡 验证要点:")
        print(f"   1. 应该看到 Detection_Example_1, Detection_Example_2, Detection_Example_3, Detection_Example_4 四个板块")
        print(f"   2. 每个板块从 step 0 开始显示")
        print(f"   3. 不同板块显示不同的图片演进")
        print(f"   4. Original_Example_1-4 显示对应的原始图片")
        print(f"   5. 拖动进度条应该看到每个板块的图片变化")
        
        # 关闭writer
        trainer.writer.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_step_0_verification():
    """创建step 0验证"""
    print(f"\n🔍 创建step 0验证...")
    
    try:
        # 创建简单的TensorBoard测试
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        test_dir = Path(f"unified_runs/step_0_verification_{timestamp}")
        test_dir.mkdir(parents=True, exist_ok=True)
        writer = SummaryWriter(test_dir / "tensorboard")
        
        print(f"📈 TensorBoard目录: {test_dir / 'tensorboard'}")
        
        # 创建测试图片
        for step in range(10):  # 从step 0到step 9
            for example_idx in range(4):  # 4个示例
                # 创建不同颜色的测试图片
                colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
                color = colors[example_idx]
                
                img = np.zeros((224, 224, 3), dtype=np.uint8)
                img[:, :] = color
                
                # 添加step和example信息
                cv2.putText(img, f"Step {step}", (50, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 3)
                cv2.putText(img, f"Example {example_idx+1}", (30, 150), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                
                # 转换为tensor
                img_tensor = torch.from_numpy(img).permute(2, 0, 1).float() / 255.0
                
                # 记录到TensorBoard
                tag = f"Step_0_Test/Example_{example_idx+1}"
                writer.add_image(tag, img_tensor, step)
        
        # 强制刷新
        writer.flush()
        writer.close()
        
        print(f"✅ Step 0验证创建完成!")
        print(f"📈 启动TensorBoard查看:")
        print(f"   tensorboard --logdir {test_dir / 'tensorboard'}")
        print(f"💡 应该看到每个Example从step 0开始显示不同颜色")
        
        return True
        
    except Exception as e:
        print(f"❌ Step 0验证创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 多示例图片可视化测试 - 从step 0开始")
    print("="*60)
    
    # 测试1：多示例可视化
    success1 = test_multi_example_visualization()
    
    # 测试2：step 0验证
    success2 = create_step_0_verification()
    
    if success1 and success2:
        print(f"\n🎉 所有测试完成!")
        print(f"✅ 多示例可视化修复验证通过")
        print(f"✅ Step 0开始显示验证通过")
        print(f"\n📋 修复要点总结:")
        print(f"   1. ✅ 从step 0开始显示")
        print(f"   2. ✅ 多个示例板块（最多4个）")
        print(f"   3. ✅ 每个板块显示不同图片的演进")
        print(f"   4. ✅ 真实BDD100K夜间图片")
    else:
        print(f"\n❌ 测试失败")
        print(f"请检查修复代码")
    
    print("="*60)

if __name__ == "__main__":
    main()
