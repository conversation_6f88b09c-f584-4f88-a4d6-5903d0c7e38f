#!/usr/bin/env python3
"""
验证真实BDD100K夜间图片显示
确保TensorBoard中显示的是真实图片而不是合成图片
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
from torch.utils.tensorboard import SummaryWriter
import time

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def verify_real_images():
    """验证真实图片显示"""
    print("🔍 验证BDD100K夜间真实图片显示")
    print("="*60)
    
    try:
        from datasets.bdd100k_night_dataset import create_bdd100k_dataloader
        
        # 创建数据加载器
        dataloader, dataset = create_bdd100k_dataloader(
            data_root="data/night",
            split="train",
            batch_size=4,
            max_samples=20,  # 只测试20张图片
            shuffle=False  # 不打乱，便于验证
        )
        
        print(f"✅ 数据集加载成功，大小: {len(dataset)}")
        print(f"📊 类别: {dataset.class_names_cn}")
        
        # 创建TensorBoard writer
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        test_dir = Path(f"unified_runs/real_image_verification_{timestamp}")
        test_dir.mkdir(parents=True, exist_ok=True)
        writer = SummaryWriter(test_dir / "tensorboard")
        
        print(f"📈 TensorBoard目录: {test_dir / 'tensorboard'}")
        
        # 测试图片显示
        print(f"\n🖼️ 测试真实图片显示...")
        
        for batch_idx, (images, targets) in enumerate(dataloader):
            print(f"   批次 {batch_idx}: 图片形状 {images.shape}")
            
            # 处理每张图片
            for img_idx in range(images.size(0)):
                global_step = batch_idx * images.size(0) + img_idx
                
                # 获取图片
                img_tensor = images[img_idx].cpu()
                
                # 反标准化 - 这是关键步骤！
                mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
                std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
                img_denorm = img_tensor * std + mean
                img_denorm = torch.clamp(img_denorm, 0, 1)
                
                # 转换为numpy用于OpenCV处理
                img_np = (img_denorm.permute(1, 2, 0).numpy() * 255).astype(np.uint8)
                
                # 获取目标信息
                target = targets[img_idx] if len(targets) > img_idx else {}
                gt_boxes = target.get('boxes', torch.tensor([])).cpu().numpy()
                gt_labels = target.get('labels', torch.tensor([])).cpu().numpy()
                
                # 创建带标注的图片
                annotated_img = img_np.copy()
                
                # 绘制真实边界框
                if len(gt_boxes) > 0:
                    h, w = img_np.shape[:2]
                    for box_idx, box in enumerate(gt_boxes):
                        # 转换为像素坐标
                        x1, y1, x2, y2 = box
                        x1, y1, x2, y2 = int(x1*w), int(y1*h), int(x2*w), int(y2*h)
                        
                        # 绘制边界框
                        cv2.rectangle(annotated_img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        
                        # 添加标签
                        if box_idx < len(gt_labels):
                            label_idx = gt_labels[box_idx]
                            if label_idx < len(dataset.class_names_cn):
                                label_text = dataset.class_names_cn[label_idx]
                                cv2.putText(annotated_img, label_text, (x1, y1-10),
                                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                # 添加图片信息
                cv2.putText(annotated_img, f"Real BDD100K Night Image {global_step+1}", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(annotated_img, f"Objects: {len(gt_boxes)}", 
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
                
                # 记录到TensorBoard
                # 1. 原始图片（反标准化后）
                writer.add_image("Real_Images/Original", img_denorm, global_step)
                
                # 2. 带标注的图片
                annotated_tensor = torch.from_numpy(annotated_img).permute(2, 0, 1).float() / 255.0
                writer.add_image("Real_Images/With_Annotations", annotated_tensor, global_step)
                
                # 3. 对比：标准化前后
                comparison_img = np.zeros((img_np.shape[0], img_np.shape[1]*2, 3), dtype=np.uint8)
                comparison_img[:, :img_np.shape[1]] = img_np  # 左侧：反标准化后
                
                # 右侧：直接从tensor转换（错误的显示方式）
                img_wrong = (img_tensor.permute(1, 2, 0).numpy() * 255).astype(np.uint8)
                img_wrong = np.clip(img_wrong, 0, 255)
                comparison_img[:, img_np.shape[1]:] = img_wrong
                
                # 添加标签
                cv2.putText(comparison_img, "Correct (Denormalized)", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(comparison_img, "Wrong (Raw Tensor)", (img_np.shape[1]+10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                
                comparison_tensor = torch.from_numpy(comparison_img).permute(2, 0, 1).float() / 255.0
                writer.add_image("Comparison/Correct_vs_Wrong", comparison_tensor, global_step)
                
                print(f"     图片 {global_step+1}: {len(gt_boxes)} 个目标，已记录到TensorBoard")
            
            if batch_idx >= 2:  # 只测试前3个批次
                break
        
        # 强制刷新
        writer.flush()
        writer.close()
        
        print(f"\n✅ 真实图片验证完成!")
        print(f"📈 启动TensorBoard查看结果:")
        print(f"   tensorboard --logdir {test_dir / 'tensorboard'}")
        print(f"   访问: http://localhost:6006")
        print(f"\n💡 验证要点:")
        print(f"   1. Real_Images/Original - 应该显示清晰的夜间道路图片")
        print(f"   2. Real_Images/With_Annotations - 应该显示带绿色边界框的图片")
        print(f"   3. Comparison/Correct_vs_Wrong - 左侧正确，右侧错误")
        print(f"   4. 如果看到清晰的夜间道路场景，说明真实图片显示正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_sample_images():
    """保存样本图片到文件"""
    print(f"\n💾 保存样本图片到文件...")
    
    try:
        from datasets.bdd100k_night_dataset import create_bdd100k_dataloader
        
        # 创建数据加载器
        dataloader, dataset = create_bdd100k_dataloader(
            data_root="data/night",
            split="train",
            batch_size=4,
            max_samples=8,
            shuffle=False
        )
        
        # 创建保存目录
        save_dir = Path("sample_real_images")
        save_dir.mkdir(exist_ok=True)
        
        # 保存样本图片
        for batch_idx, (images, targets) in enumerate(dataloader):
            for img_idx in range(images.size(0)):
                img_tensor = images[img_idx].cpu()
                
                # 反标准化
                mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
                std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
                img_denorm = img_tensor * std + mean
                img_denorm = torch.clamp(img_denorm, 0, 1)
                
                # 转换为numpy并保存
                img_np = (img_denorm.permute(1, 2, 0).numpy() * 255).astype(np.uint8)
                img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)  # OpenCV使用BGR
                
                filename = save_dir / f"real_night_sample_{batch_idx}_{img_idx}.jpg"
                cv2.imwrite(str(filename), img_bgr)
                print(f"   保存: {filename}")
            
            if batch_idx >= 1:  # 只保存前2个批次
                break
        
        print(f"✅ 样本图片保存完成，目录: {save_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 保存样本图片失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 BDD100K夜间真实图片显示验证")
    print("="*60)
    
    # 验证TensorBoard显示
    success1 = verify_real_images()
    
    # 保存样本图片
    success2 = save_sample_images()
    
    if success1 and success2:
        print(f"\n🎉 验证完成!")
        print(f"✅ 真实图片显示正确")
        print(f"📁 样本图片已保存到 sample_real_images/ 目录")
        print(f"📈 请启动TensorBoard查看可视化结果")
    else:
        print(f"\n❌ 验证失败")
        print(f"请检查数据集路径和图片处理逻辑")
    
    print("="*60)

if __name__ == "__main__":
    main()
