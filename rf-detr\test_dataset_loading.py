#!/usr/bin/env python3
"""
测试数据集加载
验证BDD100K夜间数据集是否能正常加载
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datasets.bdd100k_night_dataset import create_bdd100k_dataloader

def test_dataset_loading():
    """测试数据集加载"""
    print("🧪 测试BDD100K夜间数据集加载...")
    print("="*60)
    
    try:
        # 测试小批量加载
        dataloader, dataset = create_bdd100k_dataloader(
            data_root="data/night",
            split="train",
            batch_size=4,
            max_samples=20,  # 只测试20张图片
            shuffle=True
        )
        
        print(f"✅ 数据集创建成功")
        print(f"📊 数据集大小: {len(dataset)}")
        print(f"🏷️ 类别数量: {len(dataset.class_names_cn)}")
        print(f"🌙 类别名称: {dataset.class_names_cn}")
        
        # 测试加载一个批次
        print(f"\n📦 测试批次加载...")
        for batch_idx, (images, targets) in enumerate(dataloader):
            print(f"   批次 {batch_idx}:")
            print(f"   图片形状: {images.shape}")
            print(f"   目标数量: {len(targets)}")
            
            for i, target in enumerate(targets):
                num_objects = target['boxes'].shape[0]
                print(f"     图片 {i}: {num_objects} 个目标")
                if num_objects > 0:
                    print(f"       标签: {target['labels'].tolist()}")
                    print(f"       边界框: {target['boxes'][:2].tolist()}")  # 显示前2个
            
            if batch_idx >= 1:  # 只测试前2个批次
                break
        
        print("\n✅ 数据集加载测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据集加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_dataset_loading()
    
    if success:
        print(f"\n🎉 数据集加载测试成功!")
        print(f"✅ 可以开始正式训练")
    else:
        print(f"\n❌ 数据集加载测试失败")
        print(f"请检查数据路径和标注文件")
    
    print("="*60)

if __name__ == "__main__":
    main()
