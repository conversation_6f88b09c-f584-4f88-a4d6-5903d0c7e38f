"""
BDD100K夜间数据集加载器
使用真实的夜间图片和COCO格式标注
"""
import torch
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image
import json
import os
import numpy as np
from pathlib import Path
import cv2
from typing import List, Dict, Tuple, Optional
import random

class BDD100KNightDataset(Dataset):
    """BDD100K夜间数据集"""
    
    def __init__(self,
                 data_root: str = "data/night",  # 修正路径为正斜杠
                 split: str = "train",  # 使用train分割获取28K图片
                 image_size: int = 512,
                 max_samples: Optional[int] = None,  # None = 使用全部图片
                 transform=None):
        """
        初始化BDD100K夜间数据集
        
        Args:
            data_root: 数据根目录
            split: 数据集分割 ("train", "val", "test")
            image_size: 图片尺寸
            max_samples: 最大样本数量
            transform: 图片变换
        """
        self.data_root = Path(data_root)
        self.split = split
        self.image_size = image_size
        self.max_samples = max_samples
        
        # 图片目录
        self.images_dir = self.data_root / "images" / "100k" / split
        
        # 标注文件
        self.annotations_file = self.data_root / "annotations" / "bdd100k_night_coco.json"
        
        # 设置变换
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((image_size, image_size)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transform
        
        # BDD100K类别映射到COCO类别
        self.bdd_to_coco_mapping = {
            'person': 0,
            'bicycle': 1, 
            'car': 2,
            'motorcycle': 3,
            'airplane': 4,
            'bus': 5,
            'train': 6,
            'truck': 7,
            'boat': 8,
            'traffic light': 9,
            'fire hydrant': 10,
            'stop sign': 11,
            'parking meter': 12,
            'bench': 13
        }
        
        # 中文类别名称
        self.class_names_cn = [
            '人', '自行车', '汽车', '摩托车', '飞机', '公交车', '火车', '卡车',
            '船', '交通灯', '消防栓', '停车标志', '停车计时器', '长椅'
        ]
        
        # 加载数据
        self.images, self.annotations = self._load_data()
        
        print(f"✅ BDD100K夜间数据集加载完成")
        print(f"📁 数据目录: {self.data_root}")
        print(f"🌙 分割: {split}")
        print(f"📊 图片数量: {len(self.images)}")
        print(f"🏷️ 标注数量: {len(self.annotations)}")
    
    def _load_data(self):
        """加载数据"""
        print(f"📂 正在加载BDD100K夜间数据集...")
        
        # 获取图片文件列表
        image_files = []
        if self.images_dir.exists():
            for ext in ['*.jpg', '*.jpeg', '*.png']:
                image_files.extend(list(self.images_dir.glob(ext)))
        
        # 限制样本数量
        if self.max_samples is not None and len(image_files) > self.max_samples:
            image_files = random.sample(image_files, self.max_samples)
        
        print(f"📸 找到 {len(image_files)} 张图片")
        
        # 加载COCO标注
        annotations = {}
        if self.annotations_file.exists():
            print(f"📋 正在加载标注文件: {self.annotations_file}")
            try:
                with open(self.annotations_file, 'r') as f:
                    coco_data = json.load(f)
                
                # 解析COCO格式标注
                annotations = self._parse_coco_annotations(coco_data, image_files)
                print(f"✅ 标注加载完成，共 {len(annotations)} 个图片有标注")
                
            except Exception as e:
                print(f"⚠️ 标注文件加载失败: {e}")
                print("🔄 将使用空标注")
                annotations = {}
        else:
            print(f"⚠️ 标注文件不存在: {self.annotations_file}")
            annotations = {}
        
        return image_files, annotations
    
    def _parse_coco_annotations(self, coco_data, image_files):
        """解析COCO格式标注"""
        # 创建图片文件名到路径的映射
        image_name_to_path = {img_path.name: img_path for img_path in image_files}
        
        # 创建图片ID到文件名的映射
        image_id_to_filename = {}
        for img_info in coco_data.get('images', []):
            image_id_to_filename[img_info['id']] = img_info['file_name']
        
        # 创建类别ID到名称的映射
        category_id_to_name = {}
        for cat_info in coco_data.get('categories', []):
            category_id_to_name[cat_info['id']] = cat_info['name']
        
        # 解析标注
        annotations = {}
        for ann in coco_data.get('annotations', []):
            image_id = ann['image_id']
            
            # 获取图片文件名
            if image_id not in image_id_to_filename:
                continue
            
            filename = image_id_to_filename[image_id]
            
            # 检查图片是否在我们的文件列表中
            if filename not in image_name_to_path:
                continue
            
            image_path = image_name_to_path[filename]
            
            # 获取类别信息
            category_id = ann['category_id']
            category_name = category_id_to_name.get(category_id, 'unknown')
            
            # 映射到COCO类别
            if category_name in self.bdd_to_coco_mapping:
                coco_category_id = self.bdd_to_coco_mapping[category_name]
            else:
                continue  # 跳过未映射的类别
            
            # 获取边界框 (COCO格式: [x, y, width, height])
            bbox = ann['bbox']
            x, y, w, h = bbox
            
            # 转换为 [x1, y1, x2, y2] 格式
            x1, y1, x2, y2 = x, y, x + w, y + h
            
            # 初始化图片标注
            if image_path not in annotations:
                annotations[image_path] = {
                    'boxes': [],
                    'labels': [],
                    'areas': [],
                    'iscrowd': []
                }
            
            # 添加标注
            annotations[image_path]['boxes'].append([x1, y1, x2, y2])
            annotations[image_path]['labels'].append(coco_category_id)
            annotations[image_path]['areas'].append(w * h)
            annotations[image_path]['iscrowd'].append(ann.get('iscrowd', 0))
        
        return annotations
    
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        # 获取图片路径
        img_path = self.images[idx]
        
        # 加载图片
        try:
            image = Image.open(img_path).convert('RGB')
        except Exception as e:
            print(f"⚠️ 图片加载失败: {img_path}, 错误: {e}")
            # 创建一个黑色图片作为备用
            image = Image.new('RGB', (self.image_size, self.image_size), (0, 0, 0))
        
        # 获取原始图片尺寸
        orig_w, orig_h = image.size
        
        # 获取标注
        if img_path in self.annotations:
            ann = self.annotations[img_path]
            boxes = ann['boxes'].copy()
            labels = ann['labels'].copy()
            areas = ann['areas'].copy()
            iscrowd = ann['iscrowd'].copy()
            
            # 归一化边界框坐标
            normalized_boxes = []
            for box in boxes:
                x1, y1, x2, y2 = box
                # 归一化到 [0, 1]
                norm_x1 = x1 / orig_w
                norm_y1 = y1 / orig_h
                norm_x2 = x2 / orig_w
                norm_y2 = y2 / orig_h
                
                # 确保坐标在有效范围内
                norm_x1 = max(0, min(1, norm_x1))
                norm_y1 = max(0, min(1, norm_y1))
                norm_x2 = max(0, min(1, norm_x2))
                norm_y2 = max(0, min(1, norm_y2))
                
                # 确保 x2 > x1, y2 > y1
                if norm_x2 <= norm_x1:
                    norm_x2 = norm_x1 + 0.01
                if norm_y2 <= norm_y1:
                    norm_y2 = norm_y1 + 0.01
                
                normalized_boxes.append([norm_x1, norm_y1, norm_x2, norm_y2])
            
        else:
            # 没有标注的情况
            normalized_boxes = []
            labels = []
            areas = []
            iscrowd = []
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        # 创建目标字典
        target = {
            'boxes': torch.tensor(normalized_boxes, dtype=torch.float32) if normalized_boxes else torch.zeros((0, 4)),
            'labels': torch.tensor(labels, dtype=torch.long) if labels else torch.zeros((0,), dtype=torch.long),
            'image_id': torch.tensor(idx),
            'area': torch.tensor(areas, dtype=torch.float32) if areas else torch.zeros((0,)),
            'iscrowd': torch.tensor(iscrowd, dtype=torch.int64) if iscrowd else torch.zeros((0,), dtype=torch.int64),
            'orig_size': torch.tensor([orig_h, orig_w]),
            'size': torch.tensor([self.image_size, self.image_size])
        }
        
        return image, target

def collate_fn_bdd(batch):
    """自定义批处理函数 - 移到模块级别避免pickle问题"""
    images, targets = zip(*batch)
    images = torch.stack(images)
    return images, list(targets)

def create_bdd100k_dataloader(data_root: str = "data/night",  # 修正路径为正斜杠
                             split: str = "train",  # 使用train获取28K图片
                             batch_size: int = 32,
                             image_size: int = 512,
                             max_samples: Optional[int] = None,  # None = 全部图片
                             num_workers: int = 0,
                             shuffle: bool = True):
    """创建BDD100K夜间数据加载器"""
    
    dataset = BDD100KNightDataset(
        data_root=data_root,
        split=split,
        image_size=image_size,
        max_samples=max_samples
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        collate_fn=collate_fn_bdd,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    return dataloader, dataset

if __name__ == "__main__":
    # 测试BDD100K夜间数据集
    print("🧪 测试BDD100K夜间数据集...")
    
    try:
        dataloader, dataset = create_bdd100k_dataloader(
            data_root=r"D:\vscode\Roo_Code\hvi-rf-detr\data\night",
            split="val",
            batch_size=2,
            max_samples=50,
            shuffle=True
        )
        
        print(f"✅ 数据集创建成功")
        print(f"📊 数据集大小: {len(dataset)}")
        print(f"🏷️ 类别数量: {len(dataset.class_names_cn)}")
        print(f"🌙 类别名称: {dataset.class_names_cn}")
        
        # 测试加载一个批次
        for batch_idx, (images, targets) in enumerate(dataloader):
            print(f"\n📦 批次 {batch_idx}:")
            print(f"   图片形状: {images.shape}")
            print(f"   目标数量: {len(targets)}")
            
            for i, target in enumerate(targets):
                num_objects = target['boxes'].shape[0]
                print(f"   图片 {i}: {num_objects} 个目标")
                if num_objects > 0:
                    print(f"     标签: {target['labels'].tolist()}")
                    print(f"     边界框: {target['boxes'][:3].tolist()}")  # 显示前3个
            
            if batch_idx >= 2:  # 只测试前3个批次
                break
        
        print("\n✅ BDD100K夜间数据集测试完成!")
        
    except Exception as e:
        print(f"❌ 数据集测试失败: {e}")
        import traceback
        traceback.print_exc()
