#!/usr/bin/env python3
"""
快速训练测试 - 验证单图片迭代可视化修复效果
运行几个迭代来验证TensorBoard可视化是否正常工作
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unified_full_training_28k import UnifiedTrainingConfig, UnifiedTrainer

def quick_test():
    """快速测试训练系统"""
    print("🧪 快速训练测试 - 验证单图片迭代可视化")
    print("="*60)
    
    # 创建测试配置 - 只运行很少的迭代
    config = UnifiedTrainingConfig(
        epochs=1,  # 只运行1个epoch
        batch_size=4,  # 小批次
        learning_rate=1e-4,
        target_fps=30.0,
        use_hvi_enhancement=True,
        use_multi_scale_fusion=True,
        use_eiou_loss=True,
        use_attention_optimization=True,
        log_interval=1,  # 每次迭代都记录
        viz_interval=1,  # 每次迭代都可视化
        max_samples=20   # 只使用20张图片进行测试
    )
    
    print(f"📊 测试配置:")
    print(f"   - Epochs: {config.epochs}")
    print(f"   - Batch Size: {config.batch_size}")
    print(f"   - Max Samples: {config.max_samples}")
    print(f"   - 可视化间隔: 每 {config.viz_interval} 个batch")
    
    try:
        # 创建训练器
        trainer = UnifiedTrainer(config)
        
        print(f"\n🚀 开始快速测试...")
        print(f"📁 实验目录: {trainer.experiment_dir}")
        print(f"📈 TensorBoard: tensorboard --logdir {trainer.experiment_dir / 'tensorboard'}")
        
        # 运行训练
        results = trainer.run_unified_training()
        
        print(f"\n✅ 快速测试完成!")
        print(f"📁 实验目录: {results['experiment_dir']}")
        print(f"📊 数据集大小: {results['dataset_size']} 张图片")
        print(f"🏆 最终mAP: {results['best_mAP']:.3f}")
        print(f"🚀 最终FPS: {results['final_fps']:.1f}")
        
        print(f"\n💡 验证步骤:")
        print(f"1. 启动TensorBoard:")
        print(f"   tensorboard --logdir {results['experiment_dir']}/tensorboard")
        print(f"2. 访问 http://localhost:6006")
        print(f"3. 查看 IMAGES 标签页:")
        print(f"   - Detection_Evolution/Training_Progress")
        print(f"   - Original_Images/Training_Progress")
        print(f"4. 验证每个迭代步骤只显示一张图片")
        print(f"5. 拖动进度条查看不同图片的演进")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = quick_test()
    
    if success:
        print(f"\n🎉 快速测试成功完成!")
        print(f"✅ 单图片迭代可视化修复验证通过")
    else:
        print(f"\n❌ 快速测试失败")
        print(f"请检查错误信息并修复问题")
    
    print("="*60)

if __name__ == "__main__":
    main()
